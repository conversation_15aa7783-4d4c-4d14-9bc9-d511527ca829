# Logcipher

A modern, AI-powered creative writing tool website designed for writers, students, content creators, and screenwriters.

## Features

### AI Writing Tools
- **Story Generator**: Create engaging stories across multiple genres (Fantasy, Mystery, Sci-Fi, Romance, etc.)
- **Dialogue Generator**: Perfect for screenwriters and playwrights to create natural character conversations
- **Poetry Generator**: Craft beautiful poems with various themes and styles
- **Title Generator**: Generate catchy, SEO-friendly titles for articles, blogs, books, and creative content

### Modern Design
- Responsive design that works on all devices
- Modern, tech-savvy aesthetic with artistic elements
- Rich styling with gradients, animations, and interactive elements
- Mobile-first approach with hamburger menu navigation

### Pages
- **Home**: Main landing page with all AI tools
- **About**: Information about the platform, mission, and copyright details
- **Privacy**: Comprehensive privacy policy
- **Terms**: Detailed terms of service

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **AI Backend**: Pollinations AI API
- **Styling**: Custom CSS with CSS Variables, Flexbox, and Grid
- **Icons**: Custom SVG icons for social media and popular apps
- **Responsive**: Mobile-first responsive design

## File Structure

```
ai-text2/
├── index.html              # Main homepage
├── about.html              # About page
├── privacy.html            # Privacy policy
├── terms.html              # Terms of service
├── css/
│   ├── style.css           # Main styles
│   ├── responsive.css      # Responsive design
│   ├── about.css           # About page styles
│   ├── privacy.css         # Privacy page styles
│   └── terms.css           # Terms page styles
├── js/
│   └── main.js             # Main JavaScript functionality
├── images/
│   ├── favicon.svg         # Site favicon
│   ├── twitter-icon.svg    # Social media icons
│   ├── github-icon.svg
│   ├── linkedin-icon.svg
│   ├── notion-icon.svg     # Popular app icons
│   ├── grammarly-icon.svg
│   ├── canva-icon.svg
│   └── chatgpt-icon.svg
└── assets/                 # Additional assets
```

## Getting Started

1. **Clone or download** the project files
2. **Start a local server**:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```
3. **Open your browser** and navigate to `http://localhost:8000`

## Usage

1. **Select a Tool**: Choose from Story Generator, Dialogue Generator, Poetry Generator, or Title Generator
2. **Enter Your Prompt**: Describe what you want to create
3. **Choose Options**: Select genre, style, length, and other preferences
4. **Generate Content**: Click the generate button and wait for AI-powered results
5. **Copy and Use**: Copy the generated content for your projects

## AI Integration

The website uses the Pollinations AI API for content generation:
- **Endpoint**: `https://text.pollinations.ai/{prompt}`
- **Method**: GET request with encoded prompt
- **Response**: Plain text content

## Features Highlights

### Responsive Design
- Mobile-first approach
- Hamburger menu for mobile devices
- Flexible grid layouts
- Touch-friendly interface

### Accessibility
- Semantic HTML structure
- Keyboard navigation support
- High contrast mode support
- Screen reader friendly

### Performance
- Optimized images (SVG format)
- Efficient CSS with variables
- Lazy loading for animations
- Minimal JavaScript footprint

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

This is a demonstration project. For improvements or suggestions:
1. Review the code structure
2. Test across different devices
3. Ensure accessibility compliance
4. Maintain the modern design aesthetic

## License

© 2025 Logcipher. All rights reserved.

## Contact

For questions or support, check the social media links in the website footer.

---

**Note**: This project demonstrates modern web development practices with AI integration for creative writing assistance.
