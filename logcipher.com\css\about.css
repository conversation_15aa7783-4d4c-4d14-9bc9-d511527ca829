/* About Page Specific Styles */

/* About Hero Section */
.about-hero {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.about-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.about-hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
}

.about-title {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.about-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 300;
}

.about-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* Active Navigation Link */
.nav-link.active {
    color: var(--primary-color);
    font-weight: 600;
}

.nav-link.active::after {
    width: 100%;
}

/* Mission Section */
.mission-section {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.mission-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.mission-text {
    font-size: 1.1rem;
    line-height: 1.8;
}

.mission-text p {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

.mission-visual {
    display: flex;
    justify-content: center;
}

.mission-stats {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* About Features Section */
.about-features {
    padding: 6rem 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-item {
    text-align: center;
    padding: 2rem 1rem;
    border-radius: 10px;
    background-color: #F9FAFB;
    box-shadow: 0 5px 7px rgba(0, 0, 0, 0.3);

}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    display: block;
}

.feature-item h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-item p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Technology Section */
.technology-section {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.tech-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.tech-text h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.tech-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.tech-features {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.tech-features li {
    padding: 0.75rem 0;
    padding-left: 2rem;
    position: relative;
    color: var(--text-secondary);
    line-height: 1.6;
}

.tech-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.2rem;
}

.tech-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.tech-diagram {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.tech-node {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    font-weight: 600;
    text-align: center;
    min-width: 120px;
}

.tech-arrow {
    font-size: 1.5rem;
    color: var(--primary-color);
    font-weight: bold;
}

/* Team Section */
.team-section {
    padding: 6rem 0;
    background: white;
}

.commitment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.commitment-item {
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
}

.commitment-item h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.commitment-item p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Contact Section */
.contact-section {
    padding: 6rem 0;
    background: var(--bg-secondary);
    text-align: center;
}

.contact-content {
    max-width: 600px;
    margin: 0 auto;
}

.contact-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.contact-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.contact-info {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    margin-top: 2rem;
}

/* Copyright Section */
.copyright-section {
    padding: 6rem 0;
    background: white;
}

.copyright-content h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    text-align: center;
    color: var(--text-primary);
}

.copyright-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(290px, 1fr));
    gap: 2rem;
}

.copyright-item {
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
}

.copyright-item h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.copyright-item p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.copyright-item p:last-child {
    margin-bottom: 0;
}

.copyright-item strong {
    color: var(--text-primary);
}

/* Responsive Design for About Page */
@media screen and (max-width: 1024px) {

    .mission-content,
    .tech-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .mission-stats {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
    }

    .tech-diagram {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }

    .tech-arrow {
        transform: rotate(90deg);
    }
}

@media screen and (max-width: 768px) {
    .about-hero {
        padding: 6rem 0 3rem;
    }

    .about-title {
        font-size: 2.5rem;
    }

    .about-subtitle {
        font-size: 1.25rem;
    }

    .about-description {
        font-size: 1.1rem;
    }

    .mission-section,
    .about-features,
    .technology-section,
    .team-section,
    .contact-section,
    .copyright-section {
        padding: 4rem 0;
    }

    .mission-stats {
        flex-direction: column;
        align-items: center;
    }

    .stat-item {
        width: 200px;
    }

    .tech-diagram {
        flex-direction: column;
    }

    .tech-arrow {
        transform: rotate(0deg);
    }

    .commitment-grid,
    .copyright-details {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

@media screen and (max-width: 480px) {
    .about-title {
        font-size: 2rem;
    }

    .about-subtitle {
        font-size: 1.1rem;
    }

    .mission-text,
    .tech-text p,
    .contact-content p {
        font-size: 1rem;
    }

    .tech-text h2,
    .contact-content h2,
    .copyright-content h2 {
        font-size: 2rem;
    }

    .commitment-item,
    .copyright-item {
        padding: 1.5rem;
    }

    .contact-info {
        padding: 1.5rem;
    }
}