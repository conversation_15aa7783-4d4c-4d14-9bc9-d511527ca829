/* Privacy Policy Page Specific Styles */

/* Privacy Hero Section */
.privacy-hero {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.privacy-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.privacy-hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
}

.privacy-title {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.privacy-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 300;
}

.privacy-description {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    font-style: italic;
}

/* Privacy Content Section */
.privacy-content {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.privacy-sections {
    max-width: 800px;
    margin: 0 auto;
}

.privacy-section {
    background: white;
    padding: 3rem;
    margin-bottom: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.privacy-section:last-child {
    margin-bottom: 0;
}

.privacy-section h2 {
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
    position: relative;
}

.privacy-section h2::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: var(--secondary-color);
}

.privacy-section h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin: 2rem 0 1rem 0;
    font-weight: 600;
}

.privacy-section p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.privacy-section p:last-child {
    margin-bottom: 0;
}

.privacy-section ul {
    margin: 1.5rem 0;
    padding-left: 0;
    list-style: none;
}

.privacy-section li {
    padding: 0.75rem 0;
    padding-left: 2rem;
    position: relative;
    color: var(--text-secondary);
    line-height: 1.7;
    font-size: 1.1rem;
}

.privacy-section li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.5rem;
    line-height: 1.2;
}

.privacy-section strong {
    color: var(--text-primary);
    font-weight: 600;
}

/* Contact Info Styling */
.contact-info {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    margin-top: 1.5rem;
}

.contact-info p {
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.contact-info p:last-child {
    margin-bottom: 0;
}

.contact-info strong {
    color: var(--primary-color);
    font-size: 1.1rem;
}

/* Table of Contents (if needed) */
.privacy-toc {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    margin-bottom: 3rem;
}

.privacy-toc h3 {
    font-size: 1.25rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.privacy-toc ul {
    list-style: none;
    padding: 0;
}

.privacy-toc li {
    padding: 0.5rem 0;
}

.privacy-toc a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.privacy-toc a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Highlight Boxes */
.privacy-highlight {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(236, 72, 153, 0.1));
    border: 1px solid rgba(99, 102, 241, 0.2);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 2rem 0;
}

.privacy-highlight p {
    margin-bottom: 0;
    color: var(--text-primary);
    font-weight: 500;
}

/* Warning/Important Boxes */
.privacy-warning {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 2rem 0;
}

.privacy-warning p {
    margin-bottom: 0;
    color: #dc2626;
    font-weight: 500;
}

/* Responsive Design */
@media screen and (max-width: 1024px) {
    .privacy-sections {
        max-width: 90%;
    }
    
    .privacy-section {
        padding: 2.5rem;
    }
}

@media screen and (max-width: 768px) {
    .privacy-hero {
        padding: 6rem 0 3rem;
    }
    
    .privacy-title {
        font-size: 2.5rem;
    }
    
    .privacy-subtitle {
        font-size: 1.25rem;
    }
    
    .privacy-content {
        padding: 4rem 0;
    }
    
    .privacy-sections {
        max-width: 95%;
    }
    
    .privacy-section {
        padding: 2rem;
        margin-bottom: 1.5rem;
    }
    
    .privacy-section h2 {
        font-size: 1.75rem;
    }
    
    .privacy-section h3 {
        font-size: 1.25rem;
    }
    
    .privacy-section p,
    .privacy-section li {
        font-size: 1rem;
    }
    
    .contact-info {
        padding: 1.5rem;
    }
}

@media screen and (max-width: 480px) {
    .privacy-hero {
        padding: 5rem 0 2rem;
    }
    
    .privacy-title {
        font-size: 2rem;
    }
    
    .privacy-subtitle {
        font-size: 1.1rem;
    }
    
    .privacy-section {
        padding: 1.5rem;
    }
    
    .privacy-section h2 {
        font-size: 1.5rem;
    }
    
    .privacy-section h3 {
        font-size: 1.1rem;
    }
    
    .privacy-section li {
        padding-left: 1.5rem;
        font-size: 0.95rem;
    }
    
    .contact-info {
        padding: 1rem;
    }
    
    .privacy-highlight,
    .privacy-warning {
        padding: 1rem;
        margin: 1.5rem 0;
    }
}

/* Print Styles */
@media print {
    .privacy-hero {
        background: white !important;
        color: black !important;
        padding: 2rem 0;
    }
    
    .privacy-hero::before {
        display: none;
    }
    
    .privacy-title,
    .privacy-subtitle {
        color: black !important;
    }
    
    .privacy-section {
        box-shadow: none;
        border: 1px solid #ccc;
        break-inside: avoid;
        margin-bottom: 1rem;
        padding: 1.5rem;
    }
    
    .privacy-section h2 {
        color: black !important;
        border-bottom-color: #ccc;
    }
    
    .privacy-section h2::after {
        background: #ccc;
    }
    
    .contact-info,
    .privacy-highlight,
    .privacy-warning {
        border: 1px solid #ccc;
        background: #f9f9f9 !important;
    }
    
    a {
        color: black !important;
        text-decoration: underline;
    }
}

/* Accessibility Improvements */
.privacy-section:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.privacy-section a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .privacy-section {
        border: 2px solid #000;
    }
    
    .privacy-section h2 {
        border-bottom-color: #000;
    }
    
    .privacy-section h2::after {
        background: #000;
    }
    
    .privacy-section li::before {
        color: #000;
    }
    
    .contact-info {
        border-left-color: #000;
        background: #f0f0f0;
    }
}
