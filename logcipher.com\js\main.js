// Logcipher - Main JavaScript

// Global variables
let currentTool = '';
const API_BASE_URL = 'https://text.pollinations.ai';

// DOM Elements
const modal = document.getElementById('toolModal');
const modalTitle = document.getElementById('modalTitle');
const promptInput = document.getElementById('promptInput');
const inputOptions = document.getElementById('inputOptions');
const loading = document.getElementById('loading');
const result = document.getElementById('result');
const mobileMenu = document.getElementById('mobile-menu');
const navMenu = document.querySelector('.nav-menu');

// Tool configurations
const toolConfigs = {
    story: {
        title: 'AI Story Generator',
        placeholder: 'Describe the story you want to create (e.g., "A magical adventure about a young wizard discovering their powers")',
        options: [
            {
                label: 'Genre',
                name: 'genre',
                type: 'select',
                options: ['Fantasy', 'Mystery', 'Sci-Fi', 'Romance', 'Horror', 'Adventure', 'Comedy', 'Drama']
            },
            {
                label: 'Length',
                name: 'length',
                type: 'select',
                options: ['Short (100-200 words)', 'Medium (300-500 words)', 'Long (500-800 words)']
            },
            {
                label: 'Target Audience',
                name: 'audience',
                type: 'select',
                options: ['Children', 'Young Adult', 'Adult', 'All Ages']
            }
        ]
    },
    dialogue: {
        title: 'Dialogue Generator',
        placeholder: 'Describe the dialogue scene (e.g., "Two detectives discussing a mysterious case in a coffee shop")',
        options: [
            {
                label: 'Scene Type',
                name: 'scene',
                type: 'select',
                options: ['Dramatic', 'Comedy', 'Action', 'Romance', 'Thriller', 'Casual Conversation']
            },
            {
                label: 'Number of Characters',
                name: 'characters',
                type: 'select',
                options: ['2 Characters', '3 Characters', '4+ Characters']
            },
            {
                label: 'Tone',
                name: 'tone',
                type: 'select',
                options: ['Serious', 'Light-hearted', 'Tense', 'Emotional', 'Professional', 'Friendly']
            }
        ]
    },
    poetry: {
        title: 'Poetry Generator',
        placeholder: 'Describe the poem theme or emotion (e.g., "A poem about autumn leaves and the passage of time")',
        options: [
            {
                label: 'Style',
                name: 'style',
                type: 'select',
                options: ['Free Verse', 'Rhyming', 'Haiku', 'Sonnet', 'Limerick', 'Acrostic']
            },
            {
                label: 'Theme',
                name: 'theme',
                type: 'select',
                options: ['Love', 'Nature', 'Life', 'Dreams', 'Friendship', 'Hope', 'Memories', 'Adventure']
            },
            {
                label: 'Mood',
                name: 'mood',
                type: 'select',
                options: ['Uplifting', 'Melancholic', 'Peaceful', 'Energetic', 'Romantic', 'Contemplative']
            }
        ]
    },
    title: {
        title: 'Title Generator',
        placeholder: 'Describe your content (e.g., "A blog post about sustainable living tips for beginners")',
        options: [
            {
                label: 'Content Type',
                name: 'type',
                type: 'select',
                options: ['Blog Post', 'Article', 'Book', 'Story', 'Essay', 'Report', 'Newsletter', 'Social Media']
            },
            {
                label: 'Style',
                name: 'style',
                type: 'select',
                options: ['Catchy', 'Professional', 'Creative', 'SEO-Friendly', 'Question-Based', 'List-Based']
            },
            {
                label: 'Number of Titles',
                name: 'count',
                type: 'select',
                options: ['3 titles', '5 titles', '10 titles']
            }
        ]
    }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeSmoothScrolling();
    initializeAnimations();
});

// Navigation functionality
function initializeNavigation() {
    // Mobile menu toggle
    if (mobileMenu && navMenu) {
        mobileMenu.addEventListener('click', function() {
            mobileMenu.classList.toggle('active');
            navMenu.classList.toggle('active');
        });

        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }

    // Header scroll effect
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.header');
        if (window.scrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.98)';
            header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.boxShadow = 'none';
        }
    });
}

// Smooth scrolling for anchor links
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Initialize animations
function initializeAnimations() {
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.tool-card, .feature-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Tool tab switching functions
function switchTool(toolType) {
    // Remove active class from all tabs
    document.querySelectorAll('.tool-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // Hide all panels
    document.querySelectorAll('.tool-panel').forEach(panel => {
        panel.classList.remove('active');
    });

    // Activate selected tab and panel
    const selectedTab = document.querySelector(`[data-tool="${toolType}"]`);
    const selectedPanel = document.getElementById(`panel-${toolType}`);

    if (selectedTab && selectedPanel) {
        selectedTab.classList.add('active');
        selectedPanel.classList.add('active');

        // Focus on textarea after a short delay
        setTimeout(() => {
            const textarea = selectedPanel.querySelector('textarea');
            if (textarea) textarea.focus();
        }, 100);

        // Scroll to tools section
        document.getElementById('tools').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

function generateOptions(options) {
    inputOptions.innerHTML = '';
    
    options.forEach(option => {
        const optionGroup = document.createElement('div');
        optionGroup.className = 'option-group';
        
        const label = document.createElement('label');
        label.textContent = option.label;
        label.setAttribute('for', option.name);
        
        const select = document.createElement('select');
        select.id = option.name;
        select.name = option.name;
        
        // Add default option
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = `Select ${option.label}`;
        select.appendChild(defaultOption);
        
        // Add options
        option.options.forEach(optionValue => {
            const optionElement = document.createElement('option');
            optionElement.value = optionValue;
            optionElement.textContent = optionValue;
            select.appendChild(optionElement);
        });
        
        optionGroup.appendChild(label);
        optionGroup.appendChild(select);
        inputOptions.appendChild(optionGroup);
    });
}

function closeModal() {
    modal.style.display = 'none';
    currentTool = '';
}

// Close modal when clicking outside
window.addEventListener('click', function(event) {
    if (event.target === modal) {
        closeModal();
    }
});

// Generate content using Pollinations AI
async function generateContent(toolType) {
    const promptInput = document.getElementById(`prompt-${toolType}`);
    const loading = document.getElementById(`loading-${toolType}`);
    const result = document.getElementById(`result-${toolType}`);
    const generateBtn = document.querySelector(`#panel-${toolType} .btn-generate`);

    const prompt = promptInput.value.trim();

    if (!prompt) {
        alert('Please enter a prompt to generate content.');
        return;
    }

    // Show loading state
    loading.style.display = 'block';
    result.innerHTML = '';
    generateBtn.classList.add('loading');
    generateBtn.disabled = true;

    try {
        // Collect option values for this specific tool
        const options = {};
        const panel = document.getElementById(`panel-${toolType}`);
        const selects = panel.querySelectorAll('select');
        selects.forEach(select => {
            if (select.value) {
                const optionName = select.id.split('-')[0]; // Extract option name from id
                options[optionName] = select.value;
            }
        });

        // Build enhanced prompt based on tool type and options
        const enhancedPrompt = buildEnhancedPrompt(toolType, prompt, options);

        // Call Pollinations AI API
        const response = await fetch(`${API_BASE_URL}/${encodeURIComponent(enhancedPrompt)}`, {
            method: 'GET',
            headers: {
                'Accept': 'text/plain'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const generatedText = await response.text();

        // Hide loading and show result
        loading.style.display = 'none';
        result.innerHTML = generatedText;
        generateBtn.classList.remove('loading');
        generateBtn.disabled = false;

        // Scroll to result
        result.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

    } catch (error) {
        console.error('Error generating content:', error);
        loading.style.display = 'none';
        generateBtn.classList.remove('loading');
        generateBtn.disabled = false;
        result.innerHTML = `
            <div style="color: #ef4444; text-align: center; padding: 2rem;">
                <h4>Error generating content</h4>
                <p>We're sorry, but there was an error generating your content. Please try again.</p>
                <p style="font-size: 0.875rem; margin-top: 1rem;">Error: ${error.message}</p>
            </div>
        `;
    }
}

function buildEnhancedPrompt(toolType, userPrompt, options) {
    let enhancedPrompt = '';

    switch (toolType) {
        case 'story':
            enhancedPrompt = `Write a creative ${options.genre || 'fiction'} story`;
            if (options.length) {
                const wordCount = options.length.includes('Short') ? '100-200' :
                                options.length.includes('Medium') ? '300-500' : '500-800';
                enhancedPrompt += ` of approximately ${wordCount} words`;
            }
            enhancedPrompt += `. ${userPrompt}. Make it engaging and well-structured with a clear beginning, middle, and end.`;
            break;

        case 'dialogue':
            enhancedPrompt = `Write a ${options.scene || 'dramatic'} dialogue scene`;
            if (options.characters) {
                enhancedPrompt += ` between ${options.characters.toLowerCase()}`;
            }
            enhancedPrompt += `. ${userPrompt}. Include character names and natural, realistic conversation with appropriate emotions and subtext.`;
            break;

        case 'poetry':
            enhancedPrompt = `Write a ${options.style || 'free verse'} poem`;
            if (options.theme) {
                enhancedPrompt += ` about ${options.theme.toLowerCase()}`;
            }
            enhancedPrompt += `. ${userPrompt}. Use vivid imagery, metaphors, and emotional depth.`;
            break;

        case 'title':
            enhancedPrompt = `Generate 5 ${options.style || 'catchy'} titles for a ${options.type || 'blog post'}`;
            enhancedPrompt += `. ${userPrompt}. Make them attention-grabbing, relevant, and engaging. Present them as a numbered list.`;
            break;

        default:
            enhancedPrompt = userPrompt;
    }

    return enhancedPrompt;
}

// Copy content to clipboard
async function copyContent(toolType) {
    const result = document.getElementById(`result-${toolType}`);
    const content = result.textContent || result.innerText;

    if (!content || content.trim() === '' || content.includes('Generated content will appear here')) {
        alert('No content to copy!');
        return;
    }

    try {
        await navigator.clipboard.writeText(content);

        // Show feedback
        const copyBtn = document.querySelector(`#panel-${toolType} .btn-action[onclick*="copyContent"]`);
        const originalIcon = copyBtn.querySelector('.action-icon').textContent;
        copyBtn.querySelector('.action-icon').textContent = '✅';
        copyBtn.style.background = '#10b981';
        copyBtn.style.borderColor = '#10b981';
        copyBtn.style.color = 'white';

        setTimeout(() => {
            copyBtn.querySelector('.action-icon').textContent = originalIcon;
            copyBtn.style.background = '';
            copyBtn.style.borderColor = '';
            copyBtn.style.color = '';
        }, 2000);

    } catch (err) {
        console.error('Failed to copy content: ', err);
        alert('Failed to copy content to clipboard');
    }
}

// Clear content
function clearContent(toolType) {
    const result = document.getElementById(`result-${toolType}`);
    const promptInput = document.getElementById(`prompt-${toolType}`);

    if (confirm('Are you sure you want to clear the content?')) {
        result.innerHTML = '';
        promptInput.value = '';

        // Reset all select options
        const panel = document.getElementById(`panel-${toolType}`);
        const selects = panel.querySelectorAll('select');
        selects.forEach(select => {
            select.selectedIndex = 0;
        });

        promptInput.focus();
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(event) {
    // Escape key to close modal
    if (event.key === 'Escape' && modal.style.display === 'block') {
        closeModal();
    }
    
    // Ctrl/Cmd + Enter to generate content
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter' && modal.style.display === 'block') {
        event.preventDefault();
        generateContent();
    }
});

// Add loading states to buttons
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('btn-tool')) {
        event.target.style.opacity = '0.7';
        setTimeout(() => {
            event.target.style.opacity = '1';
        }, 200);
    }
});

// Error handling for images
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        img.addEventListener('error', function() {
            this.style.display = 'none';
        });
    });
});

// Performance optimization: Lazy loading for non-critical elements
if ('IntersectionObserver' in window) {
    const lazyElements = document.querySelectorAll('.floating-card');
    const lazyObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationPlayState = 'running';
                lazyObserver.unobserve(entry.target);
            }
        });
    });
    
    lazyElements.forEach(el => {
        el.style.animationPlayState = 'paused';
        lazyObserver.observe(el);
    });
}
