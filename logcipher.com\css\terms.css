/* Terms of Service Page Specific Styles */

/* Terms Hero Section */
.terms-hero {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.terms-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.terms-hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
}

.terms-title {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.terms-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 300;
}

.terms-description {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    font-style: italic;
}

/* Terms Content Section */
.terms-content {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.terms-sections {
    max-width: 800px;
    margin: 0 auto;
}

.terms-section {
    background: white;
    padding: 3rem;
    margin-bottom: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.terms-section:last-child {
    margin-bottom: 0;
}

.terms-section h2 {
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
    position: relative;
}

.terms-section h2::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: var(--secondary-color);
}

.terms-section h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin: 2rem 0 1rem 0;
    font-weight: 600;
}

.terms-section p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.terms-section p:last-child {
    margin-bottom: 0;
}

.terms-section ul {
    margin: 1.5rem 0;
    padding-left: 0;
    list-style: none;
}

.terms-section li {
    padding: 0.75rem 0;
    padding-left: 2rem;
    position: relative;
    color: var(--text-secondary);
    line-height: 1.7;
    font-size: 1.1rem;
}

.terms-section li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.5rem;
    line-height: 1.2;
}

.terms-section strong {
    color: var(--text-primary);
    font-weight: 600;
}

/* Contact Info Styling */
.contact-info {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    margin-top: 1.5rem;
}

.contact-info p {
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.contact-info p:last-child {
    margin-bottom: 0;
}

.contact-info strong {
    color: var(--primary-color);
    font-size: 1.1rem;
}

/* Important Notice Boxes */
.terms-notice {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(236, 72, 153, 0.1));
    border: 1px solid rgba(99, 102, 241, 0.2);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 2rem 0;
}

.terms-notice p {
    margin-bottom: 0;
    color: var(--text-primary);
    font-weight: 500;
}

.terms-notice h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* Warning Boxes */
.terms-warning {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 2rem 0;
}

.terms-warning p {
    margin-bottom: 0;
    color: #dc2626;
    font-weight: 500;
}

.terms-warning h4 {
    color: #dc2626;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* Success/Info Boxes */
.terms-info {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 2rem 0;
}

.terms-info p {
    margin-bottom: 0;
    color: #059669;
    font-weight: 500;
}

.terms-info h4 {
    color: #059669;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* Table of Contents */
.terms-toc {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    margin-bottom: 3rem;
    position: sticky;
    top: 100px;
}

.terms-toc h3 {
    font-size: 1.25rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.terms-toc ul {
    list-style: none;
    padding: 0;
}

.terms-toc li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.terms-toc li:last-child {
    border-bottom: none;
}

.terms-toc a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    display: block;
}

.terms-toc a:hover {
    color: var(--primary-dark);
    padding-left: 0.5rem;
}

/* Section Numbers */
.terms-section-number {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    text-align: center;
    line-height: 30px;
    font-weight: bold;
    margin-right: 1rem;
    font-size: 0.875rem;
}

/* Responsive Design */
@media screen and (max-width: 1024px) {
    .terms-sections {
        max-width: 90%;
    }
    
    .terms-section {
        padding: 2.5rem;
    }
    
    .terms-toc {
        position: static;
        margin-bottom: 2rem;
    }
}

@media screen and (max-width: 768px) {
    .terms-hero {
        padding: 6rem 0 3rem;
    }
    
    .terms-title {
        font-size: 2.5rem;
    }
    
    .terms-subtitle {
        font-size: 1.25rem;
    }
    
    .terms-content {
        padding: 4rem 0;
    }
    
    .terms-sections {
        max-width: 95%;
    }
    
    .terms-section {
        padding: 2rem;
        margin-bottom: 1.5rem;
    }
    
    .terms-section h2 {
        font-size: 1.75rem;
    }
    
    .terms-section h3 {
        font-size: 1.25rem;
    }
    
    .terms-section p,
    .terms-section li {
        font-size: 1rem;
    }
    
    .contact-info,
    .terms-notice,
    .terms-warning,
    .terms-info {
        padding: 1.5rem;
    }
    
    .terms-toc {
        padding: 1.5rem;
    }
}

@media screen and (max-width: 480px) {
    .terms-hero {
        padding: 5rem 0 2rem;
    }
    
    .terms-title {
        font-size: 2rem;
    }
    
    .terms-subtitle {
        font-size: 1.1rem;
    }
    
    .terms-section {
        padding: 1.5rem;
    }
    
    .terms-section h2 {
        font-size: 1.5rem;
    }
    
    .terms-section h3 {
        font-size: 1.1rem;
    }
    
    .terms-section li {
        padding-left: 1.5rem;
        font-size: 0.95rem;
    }
    
    .contact-info,
    .terms-notice,
    .terms-warning,
    .terms-info {
        padding: 1rem;
        margin: 1.5rem 0;
    }
    
    .terms-toc {
        padding: 1rem;
    }
    
    .terms-section-number {
        width: 25px;
        height: 25px;
        line-height: 25px;
        font-size: 0.75rem;
        margin-right: 0.75rem;
    }
}

/* Print Styles */
@media print {
    .terms-hero {
        background: white !important;
        color: black !important;
        padding: 2rem 0;
    }
    
    .terms-hero::before {
        display: none;
    }
    
    .terms-title,
    .terms-subtitle {
        color: black !important;
    }
    
    .terms-section {
        box-shadow: none;
        border: 1px solid #ccc;
        break-inside: avoid;
        margin-bottom: 1rem;
        padding: 1.5rem;
    }
    
    .terms-section h2 {
        color: black !important;
        border-bottom-color: #ccc;
    }
    
    .terms-section h2::after {
        background: #ccc;
    }
    
    .contact-info,
    .terms-notice,
    .terms-warning,
    .terms-info {
        border: 1px solid #ccc;
        background: #f9f9f9 !important;
    }
    
    .terms-toc {
        display: none;
    }
    
    a {
        color: black !important;
        text-decoration: underline;
    }
}

/* Accessibility Improvements */
.terms-section:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.terms-section a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .terms-section {
        border: 2px solid #000;
    }
    
    .terms-section h2 {
        border-bottom-color: #000;
    }
    
    .terms-section h2::after {
        background: #000;
    }
    
    .terms-section li::before {
        color: #000;
    }
    
    .contact-info {
        border-left-color: #000;
        background: #f0f0f0;
    }
    
    .terms-notice {
        border-color: #000;
        background: #f0f0f0;
    }
    
    .terms-warning {
        border-color: #000;
        background: #ffe6e6;
    }
    
    .terms-info {
        border-color: #000;
        background: #e6ffe6;
    }
}
