/* Logcipher - Responsive Styles */

/* Tablet Styles */
@media screen and (max-width: 1024px) {
    .container {
        padding: 0 15px;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }

    .hero-visual {
        height: auto !important;
    }

    .tools-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .footer-content {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

/* Mobile Styles */
@media screen and (max-width: 768px) {

    /* Navigation */
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        height: 100vh;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-lg);
        padding: 2rem 0;
        border-top: 1px solid var(--border-color);
        backdrop-filter: blur(20px);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .nav-link {
        justify-content: center;
        padding: 1rem 2rem;
        margin: 0 1rem;
    }

    .nav-cta {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    /* Hero Section */
    .hero {
        min-height: 80vh;
        padding: 2rem 0;
    }

    .hero-container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .hero-description {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }

    .btn {
        width: 100%;
        max-width: 280px;
        margin: auto;
    }

    .hero-visual {
        height: 400px;
        margin-top: 2rem;
    }

    .hero-showcase {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .showcase-card {
        padding: 1rem;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-features {
        gap: 1rem;
    }

    .hero-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    /* Tool Tabs */
    .tool-tabs {
        gap: 0.25rem;
        margin-bottom: 2rem;
    }

    .tool-tab {
        min-width: 140px;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .tab-text {
        display: none;
    }

    .tab-icon {
        font-size: 1.5rem;
    }

    /* Tool Panels */
    .workspace-grid {
        grid-template-columns: 1fr;
        min-height: auto;
    }

    .workspace-input {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        padding: 1.5rem;
    }

    .workspace-output {
        padding: 1.5rem;
    }

    .input-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .panel-header {
        padding: 1.5rem;
    }

    .panel-title {
        font-size: 1.5rem;
    }

    .panel-description {
        font-size: 1rem;
    }

    /* Showcase */
    .showcase-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .showcase-item {
        padding: 1.5rem;
    }

    /* Sections */
    .tools-section,
    .features-section {
        padding: 4rem 0;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-description {
        font-size: 1.1rem;
    }

    .tools-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .tool-card {
        padding: 1.5rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    /* Modal */
    .modal-content {
        margin: 10% auto;
        width: 95%;
        max-height: 85vh;
    }

    .modal-header,
    .modal-body {
        padding: 1.5rem;
    }

    .input-options {
        flex-direction: column;
        gap: 1rem;
    }

    .option-group {
        min-width: auto;
    }

    /* Footer */
    .footer {
        padding: 3rem 0 2rem;
    }

    .footer-main {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-brand {
        align-items: center;
    }

    .brand-info {
        max-width: none;
    }

    .social-icons {
        justify-content: center;
    }

    .footer-links {
        justify-content: center;
    }

    .footer-tools {
        text-align: center;
    }

    .contact-info {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .social-links {
        justify-content: center;
    }

    .app-links {
        align-items: center;
    }
}

/* Small Mobile Styles */
@media screen and (max-width: 480px) {
    .container {
        padding: 0 10px;
    }

    .nav-container {
        padding: 0 15px;
    }

    .nav-logo h1 {
        font-size: 1.25rem;
    }

    .logo-tagline {
        font-size: 0.7rem;
    }

    .hero-title {
        font-size: 2rem;
        line-height: 1.2;
    }

    .hero-description {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .section-description {
        font-size: 1rem;
    }

    .tool-card {
        padding: 1.25rem;
    }

    .tool-title {
        font-size: 1.25rem;
    }

    .tool-icon {
        font-size: 2.5rem;
    }

    .feature-icon {
        font-size: 2.5rem;
    }

    .modal-content {
        margin: 5% auto;
        width: 98%;
        max-height: 90vh;
    }

    .modal-header,
    .modal-body {
        padding: 1rem;
    }

    #promptInput {
        min-height: 100px;
        padding: 0.75rem;
    }

    .floating-card {
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
    }

    /* Tool Tabs - Small Mobile */
    .tool-tabs {
        grid-template-columns: repeat(2, 1fr);
        display: grid;
        gap: 0.5rem;
    }

    .tool-tab {
        min-width: auto;
        padding: 1rem;
        flex-direction: column;
        gap: 0.25rem;
    }

    .tab-text {
        display: block;
        font-size: 0.75rem;
    }

    .tab-icon {
        font-size: 1.25rem;
    }

    /* Panel adjustments */
    .panel-header {
        padding: 1rem;
    }

    .panel-title {
        font-size: 1.25rem;
    }

    .workspace-input,
    .workspace-output {
        padding: 1rem;
    }

    .workspace-input textarea {
        min-height: 150px;
        font-size: 0.95rem;
    }

    .result {
        font-size: 0.95rem;
        padding: 1rem;
    }

    .showcase-item {
        padding: 1rem;
    }

    .showcase-title {
        font-size: 1.1rem;
    }
}

/* Large Desktop Styles */
@media screen and (min-width: 1400px) {
    .container {
        max-width: 70%;
    }

    .nav-container {
        max-width: 1400px;
    }

    .hero-container {
        max-width: 1400px;
    }

    .hero-title {
        font-size: 4rem;
    }

    .section-title {
        font-size: 3rem;
    }

    .tools-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
    .hero::before {
        background-size: 50px 50px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .floating-card {
        animation: none;
    }

    .spinner {
        animation: none;
        border: 4px solid var(--border-color);
        border-top: 4px solid var(--primary-color);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-light: #9ca3af;
        --bg-primary: #111827;
        --bg-secondary: #1f2937;
        --border-color: #374151;
    }

    .header {
        background: rgba(17, 24, 39, 0.95);
    }

    .nav-menu {
        background-color: var(--bg-primary);
    }

    .tool-card,
    .modal-content {
        background: var(--bg-secondary);
        border-color: var(--border-color);
    }

    .features-section {
        background: var(--bg-primary);
    }

    #promptInput {
        background: var(--bg-primary);
        border-color: var(--border-color);
        color: var(--text-primary);
    }

    .result {
        background: var(--bg-primary);
        border-color: var(--border-color);
    }
}

/* Print Styles */
@media print {

    .header,
    .nav-toggle,
    .hero-visual,
    .modal,
    .footer {
        display: none !important;
    }

    .hero {
        min-height: auto;
        background: white !important;
        color: black !important;
    }

    .hero-content {
        color: black !important;
    }

    .tools-section,
    .features-section {
        padding: 2rem 0;
    }

    .tool-card,
    .feature-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .btn {
        display: none;
    }

    * {
        background: white !important;
        color: black !important;
    }
}

/* Focus Styles for Accessibility */
.btn:focus,
.nav-link:focus,
#promptInput:focus,
select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --secondary-color: #ff0000;
        --text-primary: #000000;
        --text-secondary: #000000;
        --bg-primary: #ffffff;
        --border-color: #000000;
    }

    .btn-primary {
        background: #0000ff;
        border: 2px solid #000000;
    }

    .btn-secondary {
        background: #ffffff;
        color: #000000;
        border: 2px solid #000000;
    }

    .tool-card,
    .feature-card {
        border: 2px solid #000000;
    }
}